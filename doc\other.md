


# How to start project
- This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 17.3.11.
1. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.
ng serve` or `npm run start`
2. How to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.
`ng generate component component-name` or `ng g c component-name`
3. How to build the the project. The build artifacts will be stored in the `dist/` directory.
`ng build`
4. How to execute the unit tests via [Karma](https://karma-runner.github.io).
`ng test`
5. Run  to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.
`ng e2e`
6. To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.