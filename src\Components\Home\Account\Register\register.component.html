<div class="register-page">
  <!-- Hero Section -->
  <section class="register-hero">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>
    </div>
    
    <div class="hero-content">
      <div class="hero-badge">
        <span>Join Our Platform</span>
      </div>
      
      <h1 class="hero-title">
        <span class="title-word">Create Your Account</span>
      </h1>
      
      <p class="hero-description">
        Start your journey with us today and unlock amazing features
      </p>
    </div>
  </section>

  <!-- Registration Form -->
  <div class="register-content">
    <div class="content-container">
      <div class="form-wrapper">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">Sign Up</h2>
            <p class="form-subtitle">Fill in your details to create an account</p>
          </div>

          <form #registerForm="ngForm" (ngSubmit)="onSubmitForm()" autocomplete="off" class="registration-form">
            
            <!-- Name Fields Row -->
            <div class="form-row">
              <div class="form-group">
            
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  class="form-input"
                  [(ngModel)]="model.firstName"
                  required
                  #firstName="ngModel"
                  placeholder="Enter first name"
                />
                <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="error-message">
                  <span class="error-icon">⚠️</span>
                  <small *ngIf="firstName.errors?.['required']">First name is required.</small>
                </div>
              </div>

              <div class="form-group">
                
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  class="form-input"
                  [(ngModel)]="model.lastName"
                  required
                  #lastName="ngModel"
                  placeholder="Enter last name"
                />
                <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="error-message">
                  <span class="error-icon">⚠️</span>
                  <small *ngIf="lastName.errors?.['required']">Last name is required.</small>
                </div>
              </div>
            </div>

            <!-- Email Field -->
             <div class="form-row">
 <div class="form-group">
              <input
                type="email"
                id="email"
                name="email"
                class="form-input"
                [(ngModel)]="model.email"
                required
                email
                #email="ngModel"
                placeholder="Enter your email address"
              />
              <div *ngIf="email.invalid && (email.dirty || email.touched)" class="error-message">
                <span class="error-icon">⚠️</span>
                <small *ngIf="email.errors?.['required']">Email is required.</small>
                <small *ngIf="email.errors?.['email']">Please enter a valid email address.</small>
              </div>
            </div>

            <!-- Username Field -->
            <div class="form-group">
             
              <input
                type="text"
                id="username"
                name="username"
                class="form-input"
                [(ngModel)]="model.username"
                required
                #username="ngModel"
                placeholder="Choose a unique username"
              />
              <div *ngIf="username.invalid && (username.dirty || username.touched)" class="error-message">
                <span class="error-icon">⚠️</span>
                <small *ngIf="username.errors?.['required']">Username is required.</small>
              </div>
            </div>
          </div>

          <!-- dob field -->
           <div class="">
    <input
      type="date"
      id="dob"
      name="dob"
      class="form-control"
      [(ngModel)]="model.dob"
      required
      #dob="ngModel"
    />
    <div *ngIf="dob.invalid && (dob.dirty || dob.touched)" class="text-danger mt-1">
      <small *ngIf="dob.errors?.['required']">Date of birth is required.</small>
    </div>
  </div>

            <!-- Password Field -->
            <div class="form-group">
          
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                [(ngModel)]="model.password"
                required
                minlength="6"
                #password="ngModel"
                placeholder="Create a secure password"
              />
              <div class="password-strength">
                <div class="strength-bar">
                  <div class="strength-fill"></div>
                </div>
                <span class="strength-text">Password strength: Medium</span>
              </div>
              <div *ngIf="password.invalid && (password.dirty || password.touched)" class="error-message">
                <span class="error-icon">⚠️</span>
                <small *ngIf="password.errors?.['required']">Password is required.</small>
                <small *ngIf="password.errors?.['minlength']">Password must be at least 6 characters.</small>
              </div>
            </div>

            <!-- Newsletter Checkbox -->
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input
                  class="checkbox-input"
                  type="checkbox"
                  id="newsletter"
                  name="newsletter"
                  [(ngModel)]="model.newsletter"
                  #newsletter="ngModel"
                />
                <span class="checkbox-custom"></span>
                <span class="checkbox-text">
                  Subscribe to our newsletter for updates and exclusive offers
                </span>
              </label>
                <!-- Terms and Conditions -->
            <div class="terms-section mt-2">
              <p class="terms-text">
                By creating an account, you agree to our 
                <a href="#" class="terms-link">Terms of Service</a> 
                and 
                <a href="#" class="terms-link">Privacy Policy</a>
              </p>
            </div>
            </div>

          

            <!-- Form Actions -->
            <div class="form-actions">
              <button type="submit" class="submit-btn" [disabled]="registerForm.invalid">
                <span class="btn-content" *ngIf="!registerForm.invalid">
                  <span>Create Account</span>
                </span>
                <span class="btn-content" *ngIf="registerForm.invalid">
                  <span>Complete Form</span>
                </span>
              </button>
              
       
            </div>
          </form>
        </div>

        <!-- Side Panel -->
        <div class="side-panel">
          <div class="panel-content">
            <div class="panel-icon">
              <span>✨</span>
            </div>
            <h3 class="panel-title">Why Join Us?</h3>
            

            <div id="benefitCarousel" class="carousel slide" data-bs-ride="carousel">
  <div class="carousel-inner">

    <!-- Slide 1 -->
    <div class="carousel-item active">
      <div class="benefit-item text-center">
        <span class="benefit-icon">🎯</span>
        <div class="benefit-content">
          <h6>Personalized Experience</h6>
          <p>Get tailored content and recommendations</p>
        </div>
      </div>
    </div>

    <!-- Slide 2 -->
    <div class="carousel-item">
      <div class="benefit-item text-center">
        <span class="benefit-icon">🔒</span>
        <div class="benefit-content">
          <h6>Secure & Private</h6>
          <p>Your data is protected with enterprise-grade security</p>
        </div>
      </div>
    </div>

    <!-- Slide 3 -->
    <div class="carousel-item">
      <div class="benefit-item text-center">
        <span class="benefit-icon">🚀</span>
        <div class="benefit-content">
          <h6>Exclusive Features</h6>
          <p>Access premium tools and early feature releases</p>
        </div>
      </div>
    </div>

    <!-- Slide 4 -->
    <div class="carousel-item">
      <div class="benefit-item text-center">
        <span class="benefit-icon">🤝</span>
        <div class="benefit-content">
          <h6>24/7 Support</h6>
          <p>Get help whenever you need it from our team</p>
        </div>
      </div>
    </div>

  </div>

  <!-- Navigation buttons -->
  <button class="carousel-control-prev" type="button" data-bs-target="#benefitCarousel" data-bs-slide="prev" data-bs-interval="3000">
    <span class="carousel-control-prev-icon"></span>
  </button>
  <button class="carousel-control-next" type="button" data-bs-target="#benefitCarousel" data-bs-slide="next">
    <span class="carousel-control-next-icon"></span>
  </button>
</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>