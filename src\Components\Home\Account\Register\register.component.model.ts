export interface IAppUsers {
    id: number;
    userName: string;
}

export interface IRegisterUser_Request {
    firstName: string;
    lastName: string;
    email: string;
    username: string;
    password: string;
    dob: string;
    newsletter: boolean;
}

export interface IRegisterUser_Response {};

export interface ILoginUser_Request {
    usernameOrEmail: string;
    password: string;
}

export interface IUserLogin {
    firstName: string;
    token: string;
    role?: string;
}

export type ILoginUser_Response = IUserLogin;