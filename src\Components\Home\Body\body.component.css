:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.home-container {
  min-height: 100vh;
}
.apps-carousel-section {
  background-color: #f8f9fa;
}

.section-underline {
  width: 60px;
  height: 4px;
  background-color: #0d6efd;
  border-radius: 2px;
}

.app-card {
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.app-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.app-logo {
  height: 200px;
  object-fit: contain;
  padding: 1rem;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}
.card {
  border-radius: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-img-top {
  max-height: 150px;
  object-fit: contain;
}

/* Hero Section */
.hero-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
@keyframes sparkle {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
  }
}

.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}

.title-word:nth-child(1) {
  animation-delay: 0.2s;
}
.title-word:nth-child(2) {
  animation-delay: 0.4s;
}
.title-word:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #fff;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
}

/* Section Header */
.section-header {
  text-align: center;
  margin: 0 0 3rem;
}


.section-underline {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, var(--gold-color), var(--purple-color));
  margin: 0 auto;
  border-radius: 2px;
}

.maxgeneye-section {
  padding: 30px 0 60px;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}
.maxgeneye-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.body-content-container {
  display: block;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px;
  position: relative;
  background-image: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.95) 0%,
      rgba(87, 87, 87, 0.9) 50%,
      var(--gold-color) 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* All the previous Maxgeneye styles remain the same */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgb(102 102 102 / 13%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  left: 60%;
  animation-delay: 1s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Rest of the Maxgeneye styles from previous component */
.bc-left {
  display: inline-block;
  width: 50%;
  height: 100%;
  position: relative;
  z-index: 2;
}

/* .bc-left-top {
  display: inline-block;
  width: 100%;
  height: 20%;
  position: absolute;
} */

.body-content-title {
  margin: 0;
  color: white;
}

.title-letter {
  display: inline-block;
  animation: titleGlow 2s ease-in-out infinite alternate;
  transition: all 0.3s ease;
}

.title-letter:nth-child(odd) {
  animation-delay: 0.1s;
}

.title-letter:nth-child(even) {
  animation-delay: 0.2s;
}

.title-letter:hover {
  transform: scale(1.2) rotate(5deg);
  color: var(--gold-color);
}

@keyframes titleGlow {
  0% {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  100% {
    text-shadow: 2px 2px 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.4);
  }
}

.title-underline {
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--gold-color), var(--purple-color));
  margin-top: 10px;
  animation: underlineExpand 2s ease-out 0.5s forwards;
}

@keyframes underlineExpand {
  to {
    width: 200px;
  }
}
/* 
.bc-left-botton {
  display: inline-block;
  width: 100%;
  height: 80%;
  position: absolute;
  top: 20%;
} */

.description-content {
  max-width: 400px;
}

.feature-badge {
  display: inline-block;
    background: rgb(255 255 255);
    border: 1px solid var(--gold-color);
    border-radius: 20px;
    padding: 5px 11px;
    margin-bottom: 5px;
    color: var(--darkgold-color);
    font-size: 12px;
    font-weight: 600;
}

.main-description {
  color: white;
  margin-bottom: 12px;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.gold-btn {
    background: linear-gradient(45deg, #bc8710, #c1a407);
    color: white;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 25px;
    border: none;
}
.gold-outline {
  background: transparent;  
  border: 1px solid var(--darkgold-color);
  color: var(--darkgold-color);
}
.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.feature-dot {
  width: 8px;
  height: 8px;
  background: #fff;
  border-radius: 50%;
}

.button-container {
  padding: 2rem !important;
}

.enhanced-button {
  position: relative;
  color: white;
  background: transparent;
  border: 1px solid #fff;
  border-radius: 50px;
  padding: 10px 20px;
  font-size: 1rem;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
}

.enhanced-button:hover {
  transform: translateY(-3px);
}

.button-content {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 2;
}

.button-arrow {
  transition: transform 0.3s ease;
}

.enhanced-button:hover .button-arrow {
  transform: translateX(5px);
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.enhanced-button:hover .button-glow {
  left: 100%;
}

.cta-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-top: 5px; 
}

.bc-right {
  display: inline-block;
  width: 50%;
  height: 100%;
  z-index: 2;
}

.logo-container {
  display: block;
  width: 100% !important;
  max-width: 550px;
  height: 350px !important;
  position: relative;
  padding: 20px;
}

.logo-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background: #fff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.logo-glow-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.logo-image {
  position: relative;
  z-index: 2;
  max-width: 370px;
  transition: all 0.3s ease;
} 
.logo-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--gold-color);
  border-radius: 50%;
  animation: particleFloat 4s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle-2 {
  top: 30%;
  right: 20%;
  animation-delay: 1s;
}

.particle-3 {
  bottom: 30%;
  left: 30%;
  animation-delay: 2s;
}

.particle-4 {
  bottom: 20%;
  right: 30%;
  animation-delay: 3s;
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-15px);
    opacity: 1;
  }
}

.decorative-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
  height: 1px;
}

.line-1 {
  top: 25%;
  left: 0;
  width: 100%;
  animation: lineGlow 3s ease-in-out infinite;
}

.line-2 {
  top: 50%;
  left: 0;
  width: 100%;
  animation: lineGlow 3s ease-in-out infinite 1s;
}

.line-3 {
  top: 75%;
  left: 0;
  width: 100%;
  animation: lineGlow 3s ease-in-out infinite 2s;
}

@keyframes lineGlow {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .logo-container {
    padding: 0;
    height: 200px !important;
    margin-top: 20px;
  }
.maxgeneye-inner {
  display: block;
}
  
  .stat-divider {
    width: 40px;
    height: 1px;
  }

  .bc-left,
  .bc-right {
    width: 100%;
    position: relative;
  }

  .bc-right {
    top: 0;
  }

  .logo-image {
    height: 220px;
    width: 220px;
  }
}
