:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.body-background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  transition: all 0.3s ease;
}

.body-background-overlay.scrolled {
  background: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.enhanced-navbar {
  position: relative;
  padding: 0.5rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

/* Logo Section */
.logo-section {
  display: flex;
  align-items: center;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

@keyframes logoGlow {
  0% { opacity: 0.5; transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1.1); }
}

.logo-img {
  height: 70px;
  width: auto;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}
.logo-text {
  display: flex;
  flex-direction: column;
  color: white;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(45deg, var(--gold-color), white);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-tagline {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* Desktop Navigation */
.nav-links-desktop {
  display: flex;
  align-items: center;
}

/* User Info Styles */
.user-info {
  display: flex;
  align-items: center;
}
.nav-item a.cta-link.register-btn {
  background: linear-gradient(45deg,#bc8710,#c1a407);
  color: #fff
}
.user-dropdown {
  position: relative;
}

.user-button {
  background: linear-gradient(45deg,#bc8710,#c1a407);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 25px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  margin-left: 15px;
}

.user-button:hover {
  transform: translateY(-2px);
}

.user-icon {
  font-size: 1.2rem;
}

.user-name {
  font-weight: 600;
  text-transform: capitalize;
}

.user-role {
  font-size: 14px;
  line-height: normal;
  color: #fff;
  text-transform: uppercase;
}


/* Mobile Menu Styles */
.mobile-menu {
  position: fixed;
  top: 80px;
  left: 0;
  width: 100%;
  height: calc(100vh - 80px);
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 999;
  overflow-y: auto;
}

.mobile-menu.active {
  transform: translateX(0);
}

.mobile-nav-list {
  list-style: none;
  padding: 2rem 1rem;
  margin: 0;
}

.mobile-nav-list li {
  margin-bottom: 1rem;
}

.mobile-nav-link {
  display: block;
  color: white;
  text-decoration: none;
  padding: 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-link:hover {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateX(10px);
}

.mobile-cta {
  background: rgba(255, 215, 0, 0.1) !important;
  border-color: rgba(255, 215, 0, 0.3) !important;
  font-weight: 600;
}

.mobile-auth-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.mobile-user-info {
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.mobile-user-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-user-name {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.mobile-user-role {
  color: rgba(255, 215, 0, 0.8);
  font-size: 0.9rem;
  text-transform: uppercase;
  font-style: italic;
}

.mobile-logout {
  background: rgba(220, 53, 69, 0.1) !important;
  border: 1px solid rgba(220, 53, 69, 0.3) !important;
  color: white !important;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.mobile-logout:hover {
  background: rgba(220, 53, 69, 0.2) !important;
  border-color: rgba(220, 53, 69, 0.6) !important;
}
.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 10px;
  align-items: center;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-color);
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.nav-link:hover {
  color: var(--darkgold-color);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.nav-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon {
  transform: scale(1.2) rotate(10deg);
}

.nav-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--darkgold-color);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover .nav-underline {
  width: 80%;
}

/* CTA Button */
.cta-item .nav-link {
    background: transparent;
    color: var(--darkgold-color);
    font-weight: 600;
    padding: 10px 20px;
    border: 1px solid var(--darkgold-color);
}
.cta-item.login a {
    background: transparent;
    border: 1px solid var(--darkgold-color);
    margin-left: 20px;
    color: var(--darkgold-color);
}
/* .cta-item .nav-link:hover {
  background: linear-gradient(45deg, #9932cc, var(--purple-color));
  box-shadow: 0 6px 20px rgba(138, 43, 226, 0.6);
  transform: translateY(-3px);
} */

.cta-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.cta-item .nav-link:hover .cta-glow {
  left: 100%;
}

/* Mobile Menu */
.mobile-menu-button {
  display: none;
  cursor: pointer;
  padding: 0.5rem;
}

.hamburger {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #000;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(7px, 7px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -7px);
}

.mobile-nav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.98) 0%, 
    rgba(87, 87, 87, 0.95) 50%, 
    rgba(0, 0, 0, 0.98) 100%);
  backdrop-filter: blur(10px);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-nav.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 1rem;
}

.mobile-nav-list li {
  margin-bottom: 0.5rem;
}

.mobile-nav-list a {
  display: block;
  color: white;
  text-decoration: none;
  padding: 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.mobile-nav-list a:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--gold-color);
}

.mobile-cta {
  background: linear-gradient(45deg, #bc8710, #c1a407) !important;
  font-weight: 600;
}

/* Floating Particles */
.nav-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 215, 0, 0.6);
  border-radius: 50%;
}

.nav-particle-1 {
  top: 20%;
  left: 10%;
  animation: particleFloat 6s ease-in-out infinite;
}

.nav-particle-2 {
  top: 60%;
  left: 80%;
  animation: particleFloat 8s ease-in-out infinite 2s;
}

.nav-particle-3 {
  top: 40%;
  left: 50%;
  animation: particleFloat 7s ease-in-out infinite 4s;
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.6; }
  50% { transform: translateY(-20px) translateX(10px); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-links-desktop {
    display: none;
  }
  
  .mobile-menu-button {
    display: block;
  }
  
  .logo-text {
    display: none;
  }
  
  .nav-container {
    padding: 0 1rem;
  }
}
