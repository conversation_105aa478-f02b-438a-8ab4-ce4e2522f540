import { Component, HostListener, OnInit } from '@angular/core';
import { AccountService } from "../../../Utils/_services/account.service";
import { FormsModule } from '@angular/forms';
import { NgIf, AsyncPipe, CommonModule, TitleCasePipe } from '@angular/common';
import { Observable, of } from 'rxjs';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { BodyContentItemComponent } from "../../Home/Body/body.component";
import { IUserLogin } from '../../../Components/Home/Account/Register/register.component.model';
import { ToastrService } from 'ngx-toastr';


@Component({
  selector: 'app-nav',
  standalone: true,
  imports: [FormsModule, CommonModule, RouterLink, AsyncPipe],
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.css'
})
export class NavComponent implements OnInit {

  currentUser$: Observable<IUserLogin | null>;
  isScrolled = false;
  isMobileMenuOpen = false;

  constructor(
    private accountService: AccountService,
    private router: Router,
    private toastr: ToastrService
  ) {
    this.currentUser$ = this.accountService.currentUser$;
  }

  ngOnInit(): void {
    // User session is now automatically initialized in AccountService constructor
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    this.isScrolled = window.pageYOffset > 50
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen
  }

  logout() {
    this.accountService.logout();
    this.toastr.success('Logged out successfully!');
    this.isMobileMenuOpen = false; // Close mobile menu
    this.router.navigate(['/login']);
  }

  navigateToProfile() {
    // Navigate to user profile or dashboard based on role
    this.currentUser$.subscribe(user => {
      if (user?.role && (user.role.toLowerCase() === 'admin' || user.role.toLowerCase() === 'administrator')) {
        this.router.navigate(['/admin/dashboard']);
      } else {
        this.router.navigate(['/home']);
      }
    });
  }
}
