import { Component, OnInit } from '@angular/core';
import { BodyContentItemComponent } from "../../Home/Body/body.component";
import { RouterLink, RouterOutlet } from '@angular/router';

import { ToastrModule } from 'ngx-toastr';
import { NgControl } from '@angular/forms';

@Component({
  selector: 'app-body',
  standalone: true,
  imports: [ToastrModule],
  templateUrl: './body.component.html',
  styleUrl: './body.component.css'
})
export class BodyComponent implements OnInit {
  constructor() {}

  ngOnInit(): void {
  }

}
