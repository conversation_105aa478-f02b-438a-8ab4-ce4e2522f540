footer {
  background: #000;
  color: #E2E8F0;
  padding: 60px 0 0;
}

.logo-wrapper img.footer-logo {
  width: 80px;
  object-fit: contain;
  margin-bottom: 16px;
}

.logo-wrapper p {
  font-size: 14px;
  line-height: 1.6;
  color: #e9e9e9;
}

.nav-group {
  margin-bottom: 20px;
}

.nav-title {
  color: var(--darkgold-color);
  margin-bottom: 15px;
}

.nav-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-link {
  color: #E2E8F0;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}
.footer-link:hover {
  color: var(--darkgold-color);
}
.logo-wrapper {
    margin-right: 28px;
}

/* Responsive */
@media (max-width: 768px) {
  .nav-links {
  gap: 6px;
}
  footer .row {
    flex-direction: column;
    gap: 10px;
  }
  .logo-wrapper {
    margin-right: 0px;
}
}
/* Bottom Bar */
.footer-bottom {
  border-top: 1px solid rgb(255 255 255 / 20%);
  padding: 15px 0;
  position: relative;
  z-index: 2;
  margin-top: 20px;
}

.bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  margin: 0;
}

.copyright p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  margin: 0;
}

.legal-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.legal-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.legal-link:hover {
  color: var(--gold-color);
}

.separator {
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.8rem;
}