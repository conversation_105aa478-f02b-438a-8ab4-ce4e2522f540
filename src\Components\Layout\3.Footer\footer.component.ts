import { Component, OnInit } from '@angular/core';
import { AccountService } from "../../../Utils/_services/account.service";
import { FormsModule } from '@angular/forms';
import { NgIf, AsyncPipe, CommonModule, TitleCasePipe } from '@angular/common';
import { Observable, of } from 'rxjs';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';


@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.css'
})
export class FooterComponent implements OnInit {


  public year: number = 2024;

  constructor() {
  }

  ngOnInit(): void {
    this.getNewYear();
  }


   //Change copyright date
   private getNewYear() {
  
    //Create date object
    const d = new Date();

    //Get the current year
    const year = d.getFullYear();

    this.year = year;
  }
}
