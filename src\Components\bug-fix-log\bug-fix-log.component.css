:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.bug-log-page {
  min-height: 100vh;
}

/* Hero Section */
.bug-hero {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


/* Main Content */
.bug-content {
  padding: 4rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Action Header */
.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.header-info {
  flex: 1;
}

.section-title {
  margin: 0 0 0.5rem 0;
}

.section-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: start;
  gap: 0.75rem;
  padding: 9px 20px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.create-btn {
  border: 1px solid var(--gold-color);
  background-color: transparent;
  color: var(--gold-color);
}

.create-btn:hover {
  transform: translateY(-3px);
  background: linear-gradient(45deg,#bc8710,#c1a407);
  color: #fff;
}


.request-btn {
  background: linear-gradient(45deg, var(--purple-color), var(--gold-color));
  color: white;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.4);
}
.request-btn .btn-icon {
    font-size: 21px;
    line-height: normal;
}
.request-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6);
}

.btn-icon {
  font-size: 33px;
    line-height: 18px;
}

/* Statistics Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 15px;
  line-height: normal;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

/* Bug List Section */
.bug-list-section {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.list-title {
  margin: 0;
}
.Progress.status-In {
    background: #ff660024;
    color: #ff6600;
}
.status-badge {
  font-weight: 500;
  padding: 5px 10px;
    border-radius: 50px;
    font-size: 13px;
}
.status-Closed {
    background: #ff00000f;
    color: red;
}
.status-Open {
    background: #00800017;
    color: green;
}
.list-count {
  color: #000;
  font-size: 0.9rem;
  background: #f1f3f5;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}
.stat-card i {
  color: var(--darkgold-color);
}

/* Bug Grid */
.bug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}
.bug-id h6 {
  margin: 0;
}
.bug-card {
    background: #be8f0e1f;
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.bug-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: width 0.3s ease;
}

.bug-card[data-status="fixed"]::before {
  background: #28a745;
}

.bug-card[data-status="in-progress"]::before {
  background: #ffc107;
}

.bug-card[data-status="open"]::before {
  background: #dc3545;
}

.bug-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  background: white;
}

.bug-card:hover::before {
  width: 8px;
}

.bug-header {
  display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}




.status-fixed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-in-progress {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-open {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-icon {
  font-size: 1rem;
}

.bug-content {
  margin-bottom: 1.5rem;
}

.bug-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.bug-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 6px;
  border-top: 1px solid #e0e0e0;
}

.bug-date {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 13px;
}

.date-icon {
  font-size: 1rem;
}

.bug-priority {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #666;
}

.priority-dot {
  width: 8px;
  height: 8px;
  background: #ffc107;
  border-radius: 50%;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.empty-state p {
  margin-bottom: 2rem;
}

.empty-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(45deg,#bc8710,#c1a407);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.empty-action-btn:hover {
  transform: translateY(-3px);
}

/* Dialog Styles */
::ng-deep .custom-dialog .p-dialog-header {
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  color: white;
  border-radius: 15px 15px 0 0;
  padding: 15px;
  font-weight: 600;
}
::ng-deep .p-dialog-header-icon {
  color: #fff;
}

::ng-deep .custom-dialog .p-dialog-content {
  padding: 0;
  border-radius: 0 0 15px 15px;
}

::ng-deep .custom-dialog .p-dialog {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dialog-content {
  padding: 20px 15px;
}
::ng-deep .custom-dropdown {
  padding: 10px;
}
.dialog-icon {
  text-align: center;
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

/* Form Styles */
.bug-form,
.fix-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.label-icon {
  font-size: 1rem;
}

.optional-label {
  color: #999;
  font-weight: normal;
  font-size: 0.8rem;
  margin-left: 0.5rem;
}
.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

/* Custom Dropdown */
::ng-deep .custom-dropdown .p-dropdown {
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  transition: all 0.3s ease;
}

::ng-deep .custom-dropdown .p-dropdown:not(.p-disabled).p-focus {
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
}
::ng-deep .p-dialog-header-icon:hover {
    background: transparent;
}
.form-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 10px 20px;
    line-height: normal;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.cancel-btn {
  background: transparent;
  border: 2px solid #ddd;
  color: #666;
}

.cancel-btn:hover {
  border-color: #999;
  color: #333;
}

.submit-btn {
  background: linear-gradient(45deg, var(--darkgold-color), var(--gold-color));
  color: white;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
::ng-deep .p-dropdown-panel ul {
    padding: 5px 0;
    margin: 0;
}
::ng-deep .p-dropdown-panel ul li {
    margin-bottom: 5px;
    padding: 5px 10px;
    border-radius: 6px;
}
/* Responsive Design */
@media (max-width: 1024px) {
  .bug-grid {
    grid-template-columns: 1fr;
  }

  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stat-card {
    padding: 10px;
  }
  .bug-header {
    flex-direction: column-reverse;
    align-items: start;
    gap: 15px
  }
  .action-header {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-btn {
    justify-content: center;
  }

  .list-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .bug-footer {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 1rem;
  }

  .bug-list-section {
    padding: 18px;
  }

  .dialog-content {
    padding: 1.5rem;
  }
.bug-content {
  padding: 2rem 0;
}
  .bug-card {
    padding: 1rem;
  }
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--purple-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-container p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
  padding: 0.75rem 1rem;
  border: 1px solid #e0e0e0;
  background: white;
  color: #333;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--purple-color);
  color: white;
  border-color: var(--purple-color);
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.pagination-btn.active {
  background: var(--purple-color);
  color: white;
  border-color: var(--purple-color);
}

.pagination-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-btn {
  min-width: 40px;
  padding: 0.5rem;
}

.prev-btn, .next-btn {
  padding: 0.75rem 1.25rem;
  font-weight: 600;
}

.pagination-info {
  color: #666;
  font-size: 0.9rem;
  margin-left: auto;
}

/* Enhanced list header */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.list-count {
  color: #666;
  font-size: 0.9rem;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-nav {
    padding: 0.75rem;
    gap: 0.25rem;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .prev-btn, .next-btn {
    padding: 0.5rem 1rem;
  }

  .pagination-info {
    display: none;
  }

  .list-header {
    flex-direction: column;
    align-items: flex-start;
  }
}