<div class="bug-log-page">
  <!-- Hero Section -->
  <section class="bug-hero">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>
    </div>

    <div class="hero-content">
      <div class="hero-badge">
        <span>Bug Tracking System</span>
      </div>

      <h1 class="hero-title">
        <span class="title-word">Bug Fix Log</span>
      </h1>

      <p class="hero-description">
        Track, report, and manage software bugs efficiently
      </p>
    </div>
  </section>

  <!-- Main Content -->
  <div class="bug-content">
    <div class="content-container">

      <!-- Action Header -->
      <div class="action-header">
        <div class="header-info">
          <h4 class="section-title">Bug Management</h4>
          <p class="section-subtitle">Create new bugs or request fixes for existing ones</p>
        </div>
        <div class="action-buttons">
          <button class="action-btn create-btn" (click)="openCreateDialog()">
            <span>Create Bug</span>
          </button>
        </div>
      </div>

      

      <!-- Bug Statistics -->
      <div class="stats-section">
        <div class="stat-card">
          <div class="stat-icon"><i class="fa-solid fa-bug"></i></div>
          <div class="stat-content">
            <h4 class="stat-number">{{getTotalBugs()}}</h4>
            <div class="stat-label">Total Bugs</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon"><i class="fa-solid fa-circle-check"></i></div>
          <div class="stat-content">
            <h4 class="stat-number">{{getFixedBugs()}}</h4>
            <div class="stat-label">Fixed</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon"><i class="fa-solid fa-arrows-rotate"></i></div>
          <div class="stat-content">
            <h4 class="stat-number">{{getInProgressBugs()}}</h4>
            <div class="stat-label">In Progress</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon"><i class="fa-solid fa-hourglass-half"></i></div>
          <div class="stat-content">
            <h4 class="stat-number">{{getOpenBugs()}}</h4>
            <div class="stat-label">Open</div>
          </div>
        </div>
      </div>

      <!-- Bug List -->
      <div class="bug-list-section">
        <div class="list-header">
          <h4 class="list-title">Bug Reports</h4>
          <div class="list-count">{{getTotalBugs()}} total bugs</div>
        </div>

        <div class="bug-grid">
          <div class="bug-card" *ngFor="let bug of bugs; let i = index" [attr.data-status]="bug.status">
            <div class="bug-header">
              <div class="bug-id">
                <h6 class="app-label">{{bug.title}}</h6>
              </div>
              <div class="bug-status">
                <span class="status-badge" [class]="'status-' + bug.status">
                  <span class="status-icon" [ngSwitch]="bug.status">
                    <span *ngSwitchCase="'fixed'">✅</span>
                    <span *ngSwitchCase="'in-progress'">🔄</span>
                    <span *ngSwitchCase="'open'">🔴</span>
                  </span>
                  {{bug.status | titlecase}}
                </span>
              </div>
            </div>

            <div class="bug-report-content">
              <!-- <h4 class="bug-title">{{bug.title}}</h4> -->
              <p class="bug-description">{{bug.description}}</p>
            </div>

            <div class="bug-footer">
              <div class="bug-date">
                <span class="date-icon">📅</span>
                <span class="date-text">{{bug.createdAt | date: 'MMM d, y'}}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="bugs.length === 0 && !isLoading">
          <div class="empty-icon">🐛</div>
          <h3>No bugs reported yet</h3>
          <p>Start by creating your first bug report</p>
          <button class="empty-action-btn" (click)="openCreateDialog()">
            <span>Create First Bug</span>
          </button>
        </div>

        <!-- Loading State -->
        <div class="loading-container" *ngIf="isLoading">
          <div class="loading-spinner"></div>
          <p>Loading bugs...</p>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" *ngIf="bugs.length > 0 && totalPages > 1">
          <div class="pagination-nav">
            <button
              class="pagination-btn prev-btn"
              (click)="prevPage()"
              [disabled]="currentPage === 1"
            >
              ← Previous
            </button>

            <div class="pagination-numbers">
              <button
                *ngFor="let page of getPaginationArray()"
                class="pagination-btn page-btn"
                [class.active]="page === currentPage"
                (click)="goToPage(page)"
              >
                {{page}}
              </button>
            </div>

            <button
              class="pagination-btn next-btn"
              (click)="nextPage()"
              [disabled]="currentPage === totalPages"
            >
              Next →
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Bug Dialog -->
  <p-dialog
    header="Create New Bug Report"
    [(visible)]="showCreateDialog"
    [modal]="true"
    [style]="{ width: '500px' }"
    [draggable]="false"
    [resizable]="false"
    styleClass="custom-dialog"
  >
    <div class="dialog-content">
      
      <form class="bug-form">
        <!-- Select App -->
        <div class="form-group">
          <label class="form-label">
            Select App
          </label>
          <p-dropdown
            [options]="apps"
            [(ngModel)]="selectedApp"
            name="selectedApp"
            optionLabel="title"
            optionValue="id" 
            placeholder="Select an app"
            [style]="{ width: '100%' }"
            styleClass="custom-dropdown"
          ></p-dropdown>
        </div>

        <!-- Bug Title -->
        <div class="form-group">
          <label class="form-label">
            Bug Title
          </label>
          <input 
            type="text" 
            class="form-input" 
            [(ngModel)]="newBugTitle"
            name="bugTitle"
            placeholder="Enter a descriptive title for the bug"
          />
        </div>

        <!-- Description -->
        <div class="form-group">
          <label class="form-label">
            Description
          </label>
          <textarea 
            rows="4" 
            class="form-textarea" 
            [(ngModel)]="newBugDescription"
            name="bugDescription"
            placeholder="Describe the bug in detail, including steps to reproduce..."
          ></textarea>
        </div>

        <div class="form-actions">
          <button 
            type="button"
            class="form-btn cancel-btn" 
            (click)="showCreateDialog = false"
          >
            Cancel
          </button>
          <button 
            type="button"
            class="form-btn submit-btn" 
            (click)="submitNewBug()"
            [disabled]="!selectedApp || !newBugTitle || !newBugDescription"
          >
            <span>Create Bug</span>
          </button>
        </div>
      </form>
    </div>
  </p-dialog>
</div>