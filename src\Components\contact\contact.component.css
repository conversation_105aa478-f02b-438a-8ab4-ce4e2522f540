:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.contact-page {
  min-height: 100vh;
}

/* Hero Section */
.contact-hero {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Main Content */
.contact-content {
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  padding: 70px 0;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Section Headers */

.section-title {
  margin-bottom: 0.5rem;
}

.section-subtitle {
  margin: 0;
}

/* Form Section */
.form-section {
  background: white;
  border-radius: 20px;
  padding: 30px 20px;
  position: sticky;
  top: 100px;
}

/* Alerts */
.alert {
  margin-bottom: 2rem;
  border-radius: 15px;
  padding: 1rem;
  animation: slideIn 0.3s ease-out;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  border: 1px solid #c3e6cb;
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.alert-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.alert-text {
  flex: 1;
}

.alert-text strong {
  display: block;
  margin-bottom: 0.25rem;
  color: #333;
}

.alert-text p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.alert-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alert-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Styles */
.contact-form {
  margin-top: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.form-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.label-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.character-count {
  font-size: 0.8rem;
  font-weight: normal;
  color: #666;
  text-align: right;
}

.character-count.warning {
  color: #ff9800;
}
.contact-card .card-content p {
  font-size: 16px;
}
.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.form-help {
  color: #ff9800;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Submit Button */
.form-actions {
  text-align: center;
  margin-top: 1rem;
}

.submit-btn {
  background: linear-gradient(45deg, var(--darkgold-color), var(--gold-color));
  color: white;
  border: none;
  padding: 10px 25px;
  border-radius: 25px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-btn.loading {
  pointer-events: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-icon,
.btn-spinner {
  font-size: 1.2rem;
}

.btn-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Info Section */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Contact Cards */
.contact-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 2px solid transparent;
}

.contact-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--gold-color);
}

.card-icon {
  width: 50px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}
.contact-card .card-icon i {
  color: var(--darkgold-color);
}

.card-content {
  flex: 1;
}

.card-title {
  margin: 0 0 8px 0;
}

.card-text {
  margin: 0 0 0.25rem 0;
}

.card-subtext {
  color: #999;
  font-size: 0.8rem;
  margin: 0;
}

.card-action {
  color: var(--purple-color);
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 0.5rem;
  display: block;
}

/* Social Section */
.social-section {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.social-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
  text-align: center;
}

.social-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8f9fa;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: #333;
}

.social-link:hover {
  border-color: var(--gold-color);
  background: rgba(255, 215, 0, 0.1);
  transform: translateY(-2px);
}

.social-link i {
  font-size: 1.2rem;
}

.social-label {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Stats Section */
.stats-section {
  background: linear-gradient(135deg, var(--gold-color), var(--purple-color));
  border-radius: 15px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  color: white;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-grid {
     display: flex;
    flex-direction: column-reverse;
  }

  .form-section {
    position: static;
  }
}

@media (max-width: 768px) {
  .contact-content {
    padding: 40px 0;
  }
  .form-section {
    margin-bottom: 20px;
  }
.section-header {
  text-align: center;
}
  .social-links {
    grid-template-columns: 1fr;
  }
  .info-section{
    gap: 10px;
}

  .stats-section {
    flex-direction: column;
    gap: 1rem;
  }

  .contact-card {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 1rem;
  }

  .form-section {
    padding: 15px;
  }

  .contact-grid {
    gap: 2rem;
  }
}
