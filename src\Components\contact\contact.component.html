<div class="contact-page">
  <!-- Hero Section -->
  <section class="contact-hero">
    <div class="hero-background">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
      </div>
    </div>

    <div class="hero-content">
      <div class="hero-badge">
        <span>Get In Touch</span>
      </div>

      <h1 class="hero-title">
        <span class="title-word">Contact Us</span>
      </h1>

      <p class="hero-description">
        We'd love to hear from you! Please fill out the form below and we'll get in touch shortly.
      </p>
    </div>
  </section>

  <!-- Main Content -->
  <section class="contact-content">
    <div class="content-container">

      <!-- Contact Grid -->
      <div class="contact-grid">

         <!-- Contact Information -->
        <div class="info-section">
          <div class="section-header">
            <h2 class="section-title">Get in Touch</h2>
            <p class="section-subtitle">Multiple ways to reach us</p>
          </div>

          <!-- Contact Cards -->
          <div class="contact-cards">

            <!-- Address Card -->
            <div class="contact-card">
              <div class="card-icon">
                <span><i class="fa-solid fa-location-dot"></i></span>
              </div>
              <div class="card-content">
                <h6 class="card-title">Visit Our Office</h6>
                <p class="card-text">{{contactInfo.address}}</p>
              </div>
            </div>

            <!-- Phone Card -->
            <div class="contact-card">
              <div class="card-icon">
                <span><i class="fa-solid fa-phone-volume"></i></span>
              </div>
              <div class="card-content">
                <h6 class="card-title">Call Us</h6>
                <p class="card-text">{{contactInfo.phone}}</p>
                <p class="mb-0">{{contactInfo.hours}}</p>
              </div>
            </div>

            <!-- Email Card -->
            <div class="contact-card">
              <div class="card-icon">
                <span><i class="fa-solid fa-envelope"></i></span>
              </div>
              <div class="card-content">
                <h6 class="card-title">Email Us</h6>
                <p class="card-text">{{contactInfo.email}}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">Send us a Message</h3>
            <p class="section-subtitle">Fill out the form below and we'll get back to you within 24 hours</p>
          </div>

          <!-- Success Message -->
          <div class="alert alert-success" *ngIf="showSuccessMessage">
            <div class="alert-content">
              <span class="alert-icon">✅</span>
              <div class="alert-text">
                <strong>Message Sent Successfully!</strong>
                <p>Thank you for contacting us. We'll get back to you soon.</p>
              </div>
              <button class="alert-close" (click)="dismissSuccessMessage()">✕</button>
            </div>
          </div>

          <form class="contact-form" (ngSubmit)="onSubmit()" #contactForm="ngForm">
            <!-- Name Field -->
            <div class="form-group">
            
              <input type="text" id="name" class="form-input" required [(ngModel)]="formData.name" name="name"
                #name="ngModel" placeholder="Enter your full name" />
            </div>

            <!-- Email Field -->
            <div class="form-group">
              
              <input type="email" id="email" class="form-input" required [(ngModel)]="formData.email" name="email"
                #email="ngModel" placeholder="<EMAIL>" />
            </div>

            <!-- Message Field -->
            <div class="form-group">
              <textarea id="message" class="form-textarea" rows="5" required [(ngModel)]="formData.message"
                name="message" #message="ngModel"
                placeholder="Tell us about your inquiry or how we can help you..."></textarea>
                 <span class="character-count" [class.warning]="isCharacterLimitNear()">
                  {{getCharacterCount()}}/500
                </span>
              <div class="form-help" *ngIf="isCharacterLimitNear()">
                You're approaching the character limit
              </div>
            </div>


            <div class="form-actions">
              <button type="submit" class="submit-btn" [disabled]="!contactForm.valid || isSubmitting"
                [class.loading]="isSubmitting">
                {{ isSubmitting ? 'Sending...' : 'Send Message' }}
              </button>
            </div>

          </form>
        </div>
      </div>
    </div>
  </section>
</div>