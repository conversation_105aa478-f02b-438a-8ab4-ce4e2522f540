import { NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [FormsModule,NgIf],
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.css']
})
export class ContactComponent {
formData = {
    name: "",
    email: "",
    message: "",
  }

  isSubmitting = false
  showSuccessMessage = false
  showErrorMessage = false

  // Contact information
  contactInfo = {
    address: "123 Business Street, Suite 100, City, State 12345",
    phone: "+****************",
    email: "<EMAIL>",
    hours: "Mon - Fri: 9:00 AM - 6:00 PM",
  }

  // Social media links
  socialLinks = [
    { icon: "pi pi-facebook", url: "#", label: "Facebook" },
    { icon: "pi pi-twitter", url: "#", label: "Twitter" },
    { icon: "pi pi-linkedin", url: "#", label: "LinkedIn" },
    { icon: "pi pi-instagram", url: "#", label: "Instagram" },
  ]

  // Contact reasons/subjects
  contactReasons = [
    "General Inquiry",
    "Technical Support",
    "Business Partnership",
    "Career Opportunities",
    "Bug Report",
    "Feature Request",
    "Other",
  ]

  ngOnInit() {
    // Initialize component
  }

  onSubmit() {
    if (this.isSubmitting) return

    this.isSubmitting = true

    // Simulate API call delay
    setTimeout(() => {
      console.log("Form Submitted:", this.formData)

      // Show success message
      this.showSuccessMessage = true

      // Reset form
      this.formData = { name: "", email: "", message: "" }
      this.isSubmitting = false

      // Hide success message after 5 seconds
      setTimeout(() => {
        this.showSuccessMessage = false
      }, 5000)
    }, 1500) // Simulate network delay
  }

  // Utility methods for enhanced UX
  getCharacterCount(): number {
    return this.formData.message.length
  }

  isCharacterLimitNear(): boolean {
    return this.getCharacterCount() > 450 // Assuming 500 char limit
  }

  getCharacterLimitClass(): string {
    const count = this.getCharacterCount()
    if (count > 480) return "danger"
    if (count > 450) return "warning"
    return "normal"
  }

  dismissSuccessMessage() {
    this.showSuccessMessage = false
  }

  dismissErrorMessage() {
    this.showErrorMessage = false
  }

  // Contact methods
  callPhone() {
    window.open(`tel:${this.contactInfo.phone}`, "_self")
  }

  sendEmail(type = "general") {
    
  }

  openMap() {
    const address = encodeURIComponent(this.contactInfo.address)
    window.open(`https://maps.google.com/?q=${address}`, "_blank")
  }

  openSocialLink(link: any) {
    window.open(link.url, "_blank")
  }
}
