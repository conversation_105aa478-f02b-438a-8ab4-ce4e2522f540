:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.faq-page {
  min-height: 100vh;
}

/* Hero Section */
.faq-hero {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}
.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* FAQ Content */
.faq-content {
  padding: 4rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.faq-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* FAQ Header */
.faq-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  margin-bottom: 1rem;
}

.section-subtitle {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* FAQ List */
.faq-container .accordion-button:focus {
    box-shadow: none !important;
}
.faq-container .accordion-button:not(.collapsed) {
    background: transparent;
    box-shadow: none !important;
}
.faq-container .accordion-button h6 {
  color: #000;
  font-weight: 500 !important;
  margin: 0;
}
.accordion-item {
  margin-bottom: 15px;
  border-radius: 10px !important;
  overflow: hidden;
}
.faq-container  .accordion-body {
    padding: 10px 20px 20px;
}

/* Support Section */
.support-section {
  margin-top: 3rem;
}

.support-card {
  background: linear-gradient(135deg, var(--gold-color), var(--purple-color));
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.support-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.support-content {
  position: relative;
  z-index: 2;
}

.support-content h3 {
  margin-bottom: 12px;
}

.support-content p {
  margin-bottom: 2rem;
}

.support-btn {
  display: inline-flex;
  background: white;
  color: var(--darkgold-color);
  border: none;
  padding: 10px 25px;
  border-radius: 25px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.support-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: #f8f9fa;
}

.btn-icon {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {

  .faq-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .faq-question {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  .support-card {
    padding: 2rem;
  }
  .support-section {
    margin: 2rem 0;
  }
}

@media (max-width: 480px) {
  .faq-card {
    padding: 1.5rem;
  }
  .faq-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}
