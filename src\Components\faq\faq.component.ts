import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

@Component({
  selector: 'app-faq',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './faq.component.html',
  styleUrl: './faq.component.css'
})
export class FaqComponent {
   faqs = [
    {
      question: 'Do I get a free trial?',
      answer: 'No, we don\'t offer a free trial.'
    },
    {
      question: 'What is your refund policy?',
      answer: 'You can have your money back up to 48hrs after initial purchase by emailing <NAME_EMAIL>.'
    },
    {
      question: 'Do I get free updates?',
      answer: 'YES! You get 100% free auto-updates and access to all the features that we add. No need to do anything.'
    },
    {
      question: 'Where to find tutorial?',
      answer: 'The tutorial can be found by clicking here!'
    },
    {
      question: 'How will you bill me?',
      answer: 'You will be billed for the duration of your plan or until the balance is paid off. (No hidden auto renew)'
    },
    {
      question: 'Is there any limit on the number of applications?',
      answer: 'Yes, we do have a limit 25 a day applications per day for all plans.'
    },
    {
      question: 'Why is the app named <PERSON><PERSON><PERSON>?',
      answer: 'The app is named Maxgeneye because we want you to get Maximum Eyeballs aka maximum recruiters looking at your resume/job application.'
    }
  ];

}
