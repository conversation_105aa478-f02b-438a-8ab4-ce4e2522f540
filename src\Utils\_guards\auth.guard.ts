import { CanActivateFn, Router } from '@angular/router';
import { AccountService } from '../_services/account.service';
import { inject } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { map, take } from "rxjs";

export const authGuard: CanActivateFn = (_route, _state) => {
  const accountService: AccountService = inject(AccountService);
  const toastr: ToastrService = inject(ToastrService);

  return accountService.currentUser$.pipe(
    take(1),
    map((user: any) => {
      if (user) {
        return true;
      } else {
        toastr.error("You shall not pass!");
        return false;
      }
    })
  )
};

export const adminGuard: CanActivateFn = (_route, _state) => {
  const accountService: AccountService = inject(AccountService);
  const toastr: ToastrService = inject(ToastrService);
  const router: Router = inject(Router);

  return accountService.currentUser$.pipe(
    take(1),
    map((user: any) => {
      if (!user) {
        toastr.error("Please login to access this area!");
        router.navigate(['/login']);
        return false;
      }

      const userRole = user.role?.toLowerCase();
      if (userRole === 'admin' || userRole === 'administrator') {
        return true;
      } else {
        toastr.error("Access denied! Admin privileges required.");
        router.navigate(['/home']);
        return false;
      }
    })
  )
};

export const userGuard: CanActivateFn = (_route, _state) => {
  return true;
};
