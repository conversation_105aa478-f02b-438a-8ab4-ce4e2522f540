// import { CanDeactivateFn } from '@angular/router';
// import { MemberEditComponent } from '../../Components/Old/members/member-edit/member-edit.component';
// import { inject } from '@angular/core';
// import { ConfirmService } from '../_services/confirm.service';

// export const preventUnsavedChangesGuard: CanDeactivateFn<MemberEditComponent> = (component) => {

//   const confirmService = inject(ConfirmService);

//   if (component.editForm?.dirty) {
//     return confirmService.confirm() ?? false;
//   } else {
//     return true;
//   }
// };
