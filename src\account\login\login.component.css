.login-container {
            display: flex;
            height: 100vh;
        }

        /* Left Side - Image */
        .login-image {
            flex: 1;
            background: linear-gradient(45deg, #bc8710, #c1a407);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23FF6B35;stop-opacity:0.8" /><stop offset="100%" style="stop-color:%23FF8E53;stop-opacity:0.6" /></linearGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23grad1)" opacity="0.3"/><circle cx="800" cy="300" r="200" fill="url(%23grad1)" opacity="0.2"/><circle cx="400" cy="700" r="180" fill="url(%23grad1)" opacity="0.25"/><circle cx="700" cy="800" r="120" fill="url(%23grad1)" opacity="0.3"/></svg>') center/cover;
        }

        .image-content {
            text-align: center;
            color: white;
            z-index: 2;
            position: relative;
            padding: 2rem;
        }
        .welcome-text {
            margin-bottom: 10px;
        }

        .welcome-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
            max-width: 400px;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 60px;
            height: 60px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 40px;
            height: 40px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Right Side - Form */
        .login-form {
            flex: 1;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .form-container {
            width: 100%;
            max-width: 550px;
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-title {
            margin-bottom: 0.5rem;
        }

        .form-subtitle {
            color: #666;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 8px 20px 8px 10px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .form-group input::placeholder {
          font-size: 14px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #FF6B35;
            background: white;
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
        }

        .form-group input::placeholder {
            color: #999;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
.alert.alert-danger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;;
}
.alert.alert-danger p {
  margin: 0;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 3px;
}
.alert.alert-danger .btn-close {
  font-size: 12px;
}
        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .remember-me input[type="checkbox"] {
            width: auto;
            accent-color: #9e8505;
            margin: 0;
        }

        .remember-me label {
            margin: 0;
            font-size: 0.9rem;
            color: #666;
        }

        .forgot-password {
            color: #FF6B35;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-btn {
            width: 100%;
            padding: 10px 20px;
            background: linear-gradient(45deg, #bc8710, #c1a407);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .divider {
            text-align: center;
            margin: 2rem 0 0;
            position: relative;
            color: #999;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }

        .divider span {
            background: white;
            padding: 0 1rem;
            position: relative;
            font-size: 0.9rem;
        }

        button.forgot-password-link {
    border: none;
    background: transparent;
    font-size: 14px;
}
button.register-link {
    background: transparent;
    border: none;
    font-size: 16px;
    line-height: normal;
    text-decoration: underline;
}
.brand-logo img {
  max-width: 100px;
  background-color: #fff;
  padding: 10px;
  border-radius: 20px;
  margin-bottom: 1rem;
}

        .social-login {
            display: flex;
            gap: 1rem;
        }

        .social-btn {
            flex: 1;
            padding: 0.8rem;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .social-btn:hover {
            border-color: #FF6B35;
            transform: translateY(-2px);
        }

        .social-btn.google {
            color: #4285f4;
        }

        .social-btn.github {
            color: #333;
        }

        .signup-link {
            text-align: center;
            margin-top: 1rem;
            color: #666;
        }

        .signup-link a {
            color: #FF6B35;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .login-image {
            display: none;
          }
            .login-container {
                flex-direction: column;
            }

            .login-image {
                flex: 0 0 200px;
            }
            

            .welcome-subtitle {
                font-size: 1rem;
            }

            .social-login {
                flex-direction: column;
            }
        }

        @media (max-width: 480px) {
            .login-image {
                flex: 0 0 150px;
            }

            .login-form {
                padding: 2rem 1rem;
            }

            .form-container {
                max-width: 100%;
            }
        }