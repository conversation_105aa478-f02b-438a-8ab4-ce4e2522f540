<!-- <div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <div class="logo-section">
        <i class="bi bi-shield-lock-fill logo-icon"></i>
        <h3 class="login-title">Welcome Back</h3>
        <p class="login-subtitle">Sign in to your account</p>
      </div>
    </div>

    <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
      <i class="bi bi-exclamation-triangle me-2"></i>
      {{ errorMessage }}
      <button type="button" class="btn-close" (click)="clearError()"></button>
    </div>

    <form (ngSubmit)="onSubmit()" #loginForm="ngForm" class="login-form">
      <div class="form-group">
        <label for="usernameOrEmail" class="form-label">
          <i class="bi bi-person me-2"></i>Username or Email
        </label>
        <input
          type="text"
          id="usernameOrEmail"
          name="usernameOrEmail"
          class="form-control"
          [(ngModel)]="loginModel.usernameOrEmail"
          required
          placeholder="Enter your username or email"
          [disabled]="isLoading"
          #usernameOrEmail="ngModel"
        />
        <div *ngIf="usernameOrEmail.invalid && (usernameOrEmail.dirty || usernameOrEmail.touched)" class="error-message">
          <small *ngIf="usernameOrEmail.errors?.['required']">Username or email is required.</small>
        </div>
      </div>

      <div class="form-group">
        <label for="password" class="form-label">
          <i class="bi bi-lock me-2"></i>Password
        </label>
        <div class="password-input-group">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            class="form-control"
            [(ngModel)]="loginModel.password"
            required
            placeholder="Enter your password"
            [disabled]="isLoading"
            #password="ngModel"
          />
          <button
            type="button"
            class="password-toggle-btn"
            (click)="togglePasswordVisibility()"
            [disabled]="isLoading"
          >
            <i class="bi" [ngClass]="showPassword ? 'bi-eye-slash' : 'bi-eye'"></i>
          </button>
        </div>
        <div *ngIf="password.invalid && (password.dirty || password.touched)" class="error-message">
          <small *ngIf="password.errors?.['required']">Password is required.</small>
        </div>
      </div>

      <div class="form-options">
        <div class="remember-me">
          <input
            type="checkbox"
            id="rememberMe"
            name="rememberMe"
            [(ngModel)]="rememberMe"
            [disabled]="isLoading"
          />
          <label for="rememberMe" class="remember-label">Remember me</label>
        </div>
        <button
          type="button"
          class="forgot-password-link"
          (click)="navigateToForgotPassword()"
          [disabled]="isLoading"
        >
          Forgot password?
        </button>
      </div>

      <button
        type="submit"
        class="login-btn"
        [disabled]="loginForm.invalid || isLoading"
      >
        <span *ngIf="!isLoading">
          <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
        </span>
        <span *ngIf="isLoading" class="loading-content">
          <i class="bi bi-arrow-repeat spin me-2"></i>Signing In...
        </span>
      </button>
    </form>

    <div class="divider">
      <span class="divider-text">or</span>
    </div>

    <div class="register-section">
      <p class="register-text">
        Don't have an account?
        <button
          type="button"
          class="register-link"
          (click)="navigateToRegister()"
          [disabled]="isLoading"
        >
          Create one here
        </button>
      </p>
    </div>
  </div>

  <div class="background-elements">
    <div class="bg-circle bg-circle-1"></div>
    <div class="bg-circle bg-circle-2"></div>
    <div class="bg-circle bg-circle-3"></div>
  </div>
</div>  -->




<div class="login-container">
  <!-- Left Side - Image -->
  <div class="login-image">
    <div class="floating-elements">
      <div class="floating-element"></div>
      <div class="floating-element"></div>
      <div class="floating-element"></div>
    </div>

    <div class="image-content">
      <div class="brand-logo">
        <img src="https://insizon-email-templates-bucket.s3.us-east-2.amazonaws.com/insizon/insizon-logo-v2-png.png" alt="Insizon Logo" class="logo-image" />
      </div>
      <h3 class="welcome-text">Welcome Back!</h3>
      <div class="welcome-subtitle">
       We build innovative software solutions that solve real-world problems and drive business transformation.
      </div>
    </div>
  </div>

  <!-- Right Side - Form -->
  <div class="login-form">
    <div class="form-container">
      <div class="form-header">
        <h2 class="form-title">Sign In</h2>
        <p class="form-subtitle">Enter your credentials to access your account</p>
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
        <p>
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ errorMessage }}
        </p>
        <button type="button" class="btn-close" (click)="clearError()"></button>
      </div>

      <!-- Form starts -->
      <form (ngSubmit)="onSubmit()" #loginForm="ngForm">
        <div class="form-group">
          <label for="usernameOrEmail">
            <i class="fa-solid fa-envelope me-2"></i>Email Address
          </label>
          <input
            type="email"
            id="usernameOrEmail"
            name="usernameOrEmail"
            placeholder="Enter your email"
            class="form-control"
            [(ngModel)]="loginModel.usernameOrEmail"
            required
            [disabled]="isLoading"
            #usernameOrEmail="ngModel"
          />
          <div *ngIf="usernameOrEmail.invalid && (usernameOrEmail.dirty || usernameOrEmail.touched)" class="error-message">
            <small *ngIf="usernameOrEmail.errors?.['required']">Email is required.</small>
          </div>
        </div>

        <div class="form-group mb-2">
          <label for="password">
            <i class="fa-solid fa-lock me-2"></i>Password
          </label>
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            placeholder="Enter your password"
            class="form-control"
            [(ngModel)]="loginModel.password"
            required
            [disabled]="isLoading"
            #password="ngModel"
          />
          <div *ngIf="password.invalid && (password.dirty || password.touched)" class="error-message">
            <small *ngIf="password.errors?.['required']">Password is required.</small>
          </div>
        </div>

        <div class="form-options">
          <div class="remember-me">
            <input
              type="checkbox"
              id="rememberMe"
              name="rememberMe"
              [(ngModel)]="rememberMe"
              [disabled]="isLoading"
            />
            <label for="rememberMe">Remember me</label>
          </div>
          <button
            type="button"
            class="forgot-password-link"
            (click)="navigateToForgotPassword()"
            [disabled]="isLoading"
          >
            Forgot Password?
          </button>
        </div>

        <button
          type="submit"
          class="login-btn"
          [disabled]="loginForm.invalid || isLoading"
        >
          <span *ngIf="!isLoading">
            Sign In
          </span>
          <span *ngIf="isLoading">
            <i class="bi bi-arrow-repeat spin me-2"></i>Signing In...
          </span>
        </button>
      </form>

      <div class="divider">
        <span>Or continue with</span>
      </div>
      <div class="signup-link">
        Don't have an account?
        <button type="button" class="register-link" (click)="navigateToRegister()" [disabled]="isLoading">
          Sign up
        </button>
      </div>
    </div>
  </div>
</div>
