.app-dashboard {
  background-color: #f9f9f9cc;
  min-height: 100%;
  font-family: 'Segoe UI', sans-serif;
  margin-bottom: 30px;
  
}
.app-header {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-bottom: 1.5rem;
}
.add-btn i {
  line-height: 0;
}
.app-header h2 {
  font-weight: 600;
  color: #333;
}

.btn-primary {
  background-color: #BC8710;
  border-color: #BC8710;
}

.btn-primary:hover {
  background-color: #a56f0b;
  border-color: #a56f0b;
}

.pagination-summary {
  font-size: 0.875rem;
  color: #6c757d;
}

.modal-title {
  font-weight: 600;
}

.btn-sm {
    padding: 8px 20px !important;
    margin-bottom: 5px !important;
}
.edit-dlt-btn {
  display: flex;
  gap: 10px;
  align-items: center;
}
button.btn.edit-btn {
    background: #0080001f;
}
button.btn.edit-btn, button.btn.dlt-btn {
    padding: 3px 8px;
}
button.btn.dlt-btn {
    background: #ff000012;
}
button.btn.dlt-btn i {
    color: red;
}
button.btn.edit-btn i {
    color: #008000;
}
.app-dashboard table tr th {
  font-size: 15px;
}
.app-dashboard table tr td a {
  color: #000;
  text-decoration: none;
}
li.page-item.active button {
    background: var(--darkgold-color);
    border: 1px solid var(--darkgold-color);
    color: #fff !important;  
}
li.page-item button {
    border: 1px solid var(--darkgold-color);
}
ul.pagination {
    gap: 10px;
}
ul.pagination li button {
    border-radius: 4px;
    padding: 4px 10px;
    line-height: normal;
    color: #000;
}
button.page-link.prev-btn, button.page-link.next-btn {
    background: transparent;
    border: none !important;
}
/* table css */
table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
}
table th, table td {
  padding: 0.75rem 1rem;
  text-align: left;
}
table th {
  background-color: #bc87101f;
    color: #000000;
    font-weight: 600;
}
table tr td {
  font-size: 15px;
}

table tbody tr:hover {
  background-color: #f7f0e259;
}