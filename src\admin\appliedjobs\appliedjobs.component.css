.applied-jobs-container {
  background-color: #f9f9f9cc;
  border-radius: 12px;
}

.section-title {
  margin-bottom: 1rem;
  text-align: center;
}

.error-message {
  color: #d32f2f;
  text-align: center;
  font-weight: 500;
  margin-bottom: 1rem;
}

.table-wrapper {
  overflow-x: auto;
}

.view-resume-link {
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
}

.view-resume-link:hover {
  text-decoration: underline;
}

.no-data {
  text-align: center;
  font-size: 1.2rem;
  color: #888;
  margin-top: 2rem;
}



.page-btn {
  min-width: 40px;
  padding: 0.5rem;
}



.pagination-info {
  color: #666;
  font-size: 0.9rem;
  text-align: center;
  margin-top: 0.5rem;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-nav {
    padding: 0.75rem;
    gap: 0.25rem;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .prev-btn, .next-btn {
    padding: 0.5rem 1rem;
  }

  .pagination-info {
    font-size: 0.8rem;
  }
}
