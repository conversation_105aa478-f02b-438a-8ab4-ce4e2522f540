<div class="applied-jobs-container p-4">
  <div class="d-flex justify-content-between align-items-center mb-4 border-bottom pb-2">
    <div>
      <h5 class="mb-1 fw-bold">Applied Jobs</h5>
      <p class="text-muted mb-0">View all job applications submitted by users</p>
    </div>
    <div class="d-flex gap-2 align-items-center">
      <select
        class="form-select"
        style="width: auto;"
        [(ngModel)]="pageSize"
        (change)="onPageSizeChange()">
        <option value="2">2 per page</option>
        <option value="10">10 per page</option>
        <option value="25">25 per page</option>
        <option value="50">50 per page</option>
      </select>
    </div>
  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ errorMessage }}
    <button class="btn btn-sm btn-outline-danger ms-2" (click)="loadAppliedJobs()">
      <i class="bi bi-arrow-clockwise me-1"></i>Retry
    </button>
  </div>

  <!-- Applied Jobs Table -->
  <div class="table-wrapper" *ngIf="appliedJobs.length > 0">
    <table class="table">
      <thead>
        <tr>
          <th>#</th>
          <th>Employer Name</th>
          <th>Email</th>
          <th>Number</th>
          <th>Job Title</th>
          <th>Cover Letter</th>
          <th>Applied On</th>
          <th>Resume</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let job of appliedJobs; index as i">
          <td>{{ getStartIndex() + i }}</td>
          <td>{{ job.employerName }}</td>
          <td>{{ job.email || 'N/A' }}</td>
          <td>{{ job.phoneNumber || 'N/A' }}</td>
          <td>{{ job.jobEntity?.jobTitle || 'N/A' }}</td>
          <td>{{ job.coverLetterPath || 'N/A' }}</td>
          <td>{{ job.creationTime | date: 'dd/MM/yyyy' }}</td>
          <td>
            <a [href]="job.resumePath" target="_blank" class="view-resume-link">
              View Resume
            </a>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- No Applied Jobs -->
  <div *ngIf="appliedJobs.length === 0 && !errorMessage" class="text-center py-5">
    <i class="bi bi-briefcase-fill display-1 text-muted"></i>
    <h4 class="mt-3">No Applied Jobs</h4>
    <p class="text-muted">No job applications have been submitted yet.</p>
  </div>

  <!-- Pagination -->
  <nav *ngIf="totalPages > 1" aria-label="Applied jobs pagination" class="mt-4">
    <ul class="mt-3 pagination justify-content-center">
      <li class="page-item" [class.disabled]="pageNumber === 1">
        <button class="page-link prev-btn" (click)="prevPage()" [disabled]="pageNumber === 1">
          Previous
        </button>
      </li>

      <li *ngFor="let page of getPaginationArray()"
          class="page-item"
          [class.active]="page === pageNumber">
        <button class="page-link" (click)="goToPage(page)">
          {{ page }}
        </button>
      </li>

      <li class="page-item" [class.disabled]="pageNumber === totalPages">
        <button class="page-link next-btn" (click)="nextPage()" [disabled]="pageNumber === totalPages">
          Next
        </button>
      </li>
    </ul>
  </nav>
</div>
