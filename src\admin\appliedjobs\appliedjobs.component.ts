import { Component } from '@angular/core';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { AppliedJobService } from '../../services/applied-job.service';
import { CommonModule, NgFor, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-appliedjobs',
  standalone: true,
  imports: [NgIf,NgFor,CommonModule,FormsModule],
  templateUrl: './appliedjobs.component.html',
  styleUrl: './appliedjobs.component.css'
})
export class AppliedjobsComponent {

  appliedJobs: any[] = [];
  totalCount: number = 0;
  pageNumber: number = 1;
  pageSize: number = 10;
  totalPages: number = 0;
  errorMessage: string = '';

  constructor(private pageTitle : PageTitleService,
     private appliedJob : AppliedJobService
    ){}
    
  ngOnInit(): void {
  this.pageTitle.setTitle('Applied jobs');
  this.loadAppliedJobs()
 }

 loadAppliedJobs(): void {
    this.errorMessage = '';

    this.appliedJob.getAllAppliedJobs(this.pageNumber, this.pageSize).subscribe({
      next: (response) => {
        //console.log("REs",response.data.items)
        this.appliedJobs = response.data.items;
        //console.log("Job",this.appliedJob)
        this.totalCount = response.data.totalCount;
        this.totalPages = Math.ceil(this.totalCount / this.pageSize);
      },
      error: (err) => {
        console.error('Failed to fetch applied jobs', err);
        this.errorMessage = 'Failed to load applied jobs. Please try again later.';
      }
    });
  }

  nextPage(): void {
    if (this.pageNumber < this.totalPages) {
      this.pageNumber++;
      this.loadAppliedJobs();
    }
  }

  prevPage(): void {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.loadAppliedJobs();
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.pageNumber) {
      this.pageNumber = page;
      this.loadAppliedJobs();
    }
  }

  onPageSizeChange(): void {
    this.pageNumber = 1;
    this.loadAppliedJobs();
  }

  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.pageNumber - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getStartIndex(): number {
    return (this.pageNumber - 1) * this.pageSize + 1;
  }

  getEndIndex(): number {
    return Math.min(this.pageNumber * this.pageSize, this.totalCount);
  }
}
