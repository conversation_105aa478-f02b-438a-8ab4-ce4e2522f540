.header-section h3 {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-toggle {
  background-color: #6c757d;
  color: white;
}

.btn-toggle:hover {
  background-color: #5a6268;
}
.btn-sm {
  padding: 4px 8px;
  font-size: 0.875rem;
}

/* Card View Styles */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.bug-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.bug-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.card-info p {
  margin: 8px 0;
  color: #666;
}

/* Badge Styles */
.badge {
  padding: 6px 10px;
  border-radius: 50px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-high {
  background-color: #FECACA;
  color: #991B1B;
}

.priority-medium {
  background-color: #ffc107;
  color: #212529;
}

.priority-low {
  background-color: #D1FAE5;
  color: #065F46;
}

.status-open {
  background-color: #DBEAFE;
  color: #1D4ED8;
}

.status-progress {
  background-color: #FEF3C7;
  color: #92400E;
}

.status-closed {
  background-color: #E0E7FF;
  color: #4338CA;
}
.custom-select-wrapper {
  position: relative;
  width: 100%;
}
.custom-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding-right: 30px; 
  background: #fff url("data:image/svg+xml,%3Csvg fill='%23000000' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E") no-repeat right 10px center;
  background-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  height: 38px;
}


/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header h4 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-group label {
  display: block;
  font-size: 14px;
  margin-bottom: 4px;
  color: #333;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--darkgold-color);
  box-shadow: none;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 0 0 0;
  border-top: 1px solid #e0e0e0;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}
/* new */
/* Root Container */
.container {
  padding: 2rem;
  background-color: #f9f9f9;
  min-height: 100vh;
}

/* Header */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-section h3 {
  font-weight: 600;
}

.btn-toggle {
  background-color: #bc8710;
  color: #fff;
  border: none;
  padding: 0.5rem 1.2rem;
  border-radius: 5px;
}

.btn-toggle:hover {
  background-color: #a76f0e;
}

/* Card View */
.card-container {
  display: grid;
  gap: 15px;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.bug-card {
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom: 4px solid #bc8710;
  border-radius: 6px;
  padding: 1rem;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.card-info p {
  font-size: 12px;
  margin-bottom: 10px;
}

.badges-container {
  display: flex;
  gap: 5px;
  margin-top: 0.3rem;
}

/* View Button */
.btn-view {
  background-color: transparent;
  color: #bc8710;
  padding: 0.4rem 0.8rem;
  border: 1px solid #bc8710;
  font-size: 0.9rem;
  border-radius: 4px;
}

.btn-view:hover {
  background-color: #a76f0e;
  color: #fff;
}


/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 0.5rem;
}
.btn.page-link {
    border: 1px solid var(--darkgold-color) !important;
    color: #000;
}
.btn.page-link.btn-primary {
  color: #fff;
}
/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  padding: 1.5rem;
  border-radius: 6px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  animation: fadeIn 0.3s ease-in-out;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
}


.btn-primary {
  background-color: #bc8710;
  border-color: #bc8710;
}

.btn-primary:hover {
  background-color: #a76f0e;
  border-color: #a76f0e;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}


/* Responsive Design */
@media (max-width: 991px) {
  .card-container {
    grid-template-columns: 2fr;
  }
}
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header-section {
    flex-direction: column;
    gap: 10px;
  }
  
  .card-container {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }
  
  .custom-table {
    font-size: 0.875rem;
  }
  
  .custom-table th,
  .custom-table td {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  
  .modal-footer {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}