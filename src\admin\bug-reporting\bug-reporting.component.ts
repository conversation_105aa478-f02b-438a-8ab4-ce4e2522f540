import { CommonModule, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { Bug, BugReportingEntityDto, PagedResult } from '../../models/bug-report.model';
import { FormsModule, NgModel } from '@angular/forms';
import { BugReportingService } from '../../services/bug-reporting.service';
import { ToastrService, ToastrModule } from 'ngx-toastr';

@Component({
  selector: 'app-bug-reporting',
  standalone: true,
  imports: [CommonModule, FormsModule, ToastrModule],
  templateUrl: './bug-reporting.component.html',
  styleUrl: './bug-reporting.component.css'
})
export class BugReportingComponent {
  viewMode: 'card' | 'table' = 'card';
  showModal = false;
  selectedBug: BugReportingEntityDto | null = null;

  bugs: any[] = [];
  pageNumber = 1;
  pageSize = 5;
  totalItems = 0;
  totalPages = 0;

  isLoading = false;
  isSubmitting = false;

  constructor(
    private pageTitle: PageTitleService,
    private bugService: BugReportingService,
    private toastr: ToastrService
  ){}

  ngOnInit(): void {
  this.pageTitle.setTitle('Bug Reporting');
  this.loadBugs();
 }

  loadBugs() {
    this.isLoading = true;
    this.bugService.getAll(this.pageNumber, this.pageSize).subscribe({
      next: (result) => {
        this.bugs = result.data.items;
        this.totalItems = result.data.totalCount;
        this.totalPages = Math.ceil(this.totalItems / this.pageSize);
        this.isLoading = false;
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Failed to load bugs:', err);
        this.toastr.error('Error loading bugs. Please try again.', 'Error');
      }
    });
  }

   updateBug() {
    if (this.selectedBug && this.selectedBug.id) {
      this.isSubmitting = true;
      this.bugService.update(this.selectedBug.id, this.selectedBug).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.toastr.info('Bug updated successfully!', 'Success');
          this.loadBugs();
          this.closeModal();
        },
        error: (err) => {
          this.isSubmitting = false;
          console.error('Failed to update bug:', err);
          this.toastr.error(err.error?.message || 'Failed to update bug. Please try again.', 'Error');
        }
      });
    }
  }

  toggleView() {
    this.viewMode = this.viewMode === 'card' ? 'table' : 'card';
  }

  viewBug(bug: Bug) {
    this.selectedBug = { ...bug, id: String(bug.id) };
    this.showModal = true;
  }

  closeModal() {
    this.showModal = false;
    this.selectedBug = null;
  }

  // updateBug() {
  //   if (this.selectedBug) {
  //     const index = this.bugs.findIndex(b => b.id === this.selectedBug!.id);
  //     if (index !== -1) {
  //       this.bugs[index] = { ...this.selectedBug };
  //     }
  //     this.closeModal();
  //   }
  // }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Open': return 'status-open';
      case 'In Progress': return 'status-progress';
      case 'Closed': return 'status-closed';
      default: return '';
    }
  }

   changePage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.pageNumber = page;
      this.loadBugs();
    }
  }

  getVisiblePages(): number[] {
    const visiblePages: number[] = [];
    const maxVisiblePages = 5;

    if (this.totalPages <= maxVisiblePages) {
      // Show all pages if total pages is small (excluding first and last as they're handled separately)
      for (let i = 2; i < this.totalPages; i++) {
        visiblePages.push(i);
      }
    } else {
      // Show pages around current page
      let startPage = Math.max(2, this.pageNumber - 1);
      let endPage = Math.min(this.totalPages - 1, this.pageNumber + 1);

      // Ensure we don't include first and last page (they're handled separately)
      for (let i = startPage; i <= endPage; i++) {
        if (i > 1 && i < this.totalPages) {
          visiblePages.push(i);
        }
      }
    }

    return visiblePages;
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'High': return 'priority-high';
      case 'Medium': return 'priority-medium';
      case 'Low': return 'priority-low';
      default: return '';
    }
  }
  

}
