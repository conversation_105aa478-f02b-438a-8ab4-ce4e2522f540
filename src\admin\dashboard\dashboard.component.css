.text-center {
    text-align: center !important;
    color: #333333;
}

.card {
  border-radius: 0.75rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.2);
  }
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}
:host {
  background-color: #fff !important;
  display: block;
  min-height: 83vh !important;
}
/* Apply gold theme color */
.custom-btn {
  background-color: #BC8710;
  color: #fff;
  font-weight: 500;
  display: none;
  border-radius: 30px;
  transition: background 0.3s ease;
}

.custom-btn:hover {
  background-color: #a6700b;
  color: #fff;
}

/* Card Styling */
.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
  border: 1px solid #ddd !important;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
.sidebar-heading {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(87, 87, 87, 0.9) 50%, var(--gold-color) 100%) !important;

}
.card-bgmain {
  background: #f9f9f9cc;
}
.dashboard-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    color: white;
  }
.card-title {
    font-size: 20px !important;
    font-size: 500 !important;
}
  .dashboard-header {
    flex: 0 0 auto;
    padding: 1rem;
    text-align: center;
    font-weight: bold;
  }

  .dashboard-content {
    flex: 1 1 auto;
    overflow-y: auto;
    padding: 1rem;
  }

  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .custom-btn{
    background-color: #BC8710;
    color: #fff;
    font-weight: 500;
    border-radius: 30px;
  }
.badge {
  color: #000;
}
  .custom-btn:hover {
    background-color: #a06f0c;
  }

  .card-text {
    color: #6c757d !important;
  }
  .text-center {
    text-align: center !important;
    color: #333333;
    font-size: 50px !important;
}

.main-content {
    min-height: 100%;
}
.custom-badge {
  text-align: left;
  font-size: 30px;
  font-weight: 500;
}
.card-icon {
    color: #bc8710;
    background: #fff0cd;
    padding: 13px;
    border-radius: 60px;
    font-size: 22px;
    height: 50px;
    width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

  @media (max-width: 991px) {
    .dashboard-content {
      overflow-y: auto;
    }
  }