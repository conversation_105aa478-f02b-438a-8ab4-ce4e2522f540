import { Component } from '@angular/core';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { BugReportingService } from '../../services/bug-reporting.service';
import { JobService } from '../../services/job.service';
import { AccountService } from '../../Utils/_services/account.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent {

   data = {
    jobs: 0,
    users: 0,
    blogs: 0,
    bugs: 0
  };
 constructor( private pageTitle: PageTitleService,
  private bugapi : BugReportingService,
  private jobapi: JobService,
  private accountService: AccountService
 ){
 }

  ngOnInit(): void {
  this.pageTitle.setTitle('Dashboard');
  this.loadBugCount()
  this.loadJobCount()
  this.loadUserCount()
  this.loadNewsletterCount()
 }

   loadBugCount() {
    this.bugapi.getAll(1, 1).subscribe({
      next: (result) => {
        this.data.bugs = result.data.totalCount; // Set totalCount to bugs
      },
      error: (err) => {
        console.error('Failed to load bug count:', err);
      }
    });
  }

  loadJobCount() {
    this.jobapi.getJobs().subscribe({
      next: (result) => {
        if (result.data.items) {
          this.data.jobs = result.data.totalCount || result.data.length;
          //console.log('Job count loaded:', this.data.jobs);
        } else if (Array.isArray(result)) {
          this.data.jobs = result.length;
        } else {
          this.data.jobs = 0;
        }
      },
      error: (err) => {
        console.error('Failed to load job count:', err);
        this.data.jobs = 0;
      }
    });
  }

  loadUserCount() {
    this.accountService.GetAllUser(1, 1).subscribe({
      next: (result) => {
        this.data.users = result.data.totalCount || 0;
        console.log('User count loaded:', this.data.users);
      },
      error: (err) => {
        console.error('Failed to load user count:', err);
        this.data.users = 0;
      }
    });
  }

  loadNewsletterCount() {
    this.accountService.GetAllUser(1, 1000).subscribe({
      next: (result) => {
        const newsletterSubscribers = (result.data.items || []).filter((user: any) => user.newsletter === true);
        this.data.blogs = newsletterSubscribers.length;
        console.log('Newsletter subscriber count loaded:', this.data.blogs);
      },
      error: (err) => {
        console.error('Failed to load newsletter count:', err);
        this.data.blogs = 0;
      }
    });
  }

}
