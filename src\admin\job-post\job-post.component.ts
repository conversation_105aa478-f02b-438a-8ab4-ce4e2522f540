import { <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { JobService } from '../../services/job.service';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { ToastrService } from 'ngx-toastr';
declare var bootstrap: any;

@Component({
  selector: 'app-job-post',
  standalone: true,
  imports: [FormsModule,NgFor,NgIf],
  templateUrl: './job-post.component.html',
  styleUrl: './job-post.component.css'
})
export class JobPostComponent {

  jobs: any[] = [];
  totalCount: number = 0;
  pageNumber: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  isLoading: boolean = false;
  errorMessage: string = '';
  showCreateForm = false;
  job: any = {
    jobId:null,
    jobTitle: '',
    company: '',
    description: '',
    jobType: '',
    location: '',
    mode: '',
    salaryPackage: null,
    requirements: ''
  };
  modalMode: 'create' | 'edit' = 'create';
  jobToDelete: { index: number; id: number; title: string } | null = null;
  isDeleting: boolean = false;

 constructor(private jobService: JobService,private pageTitle: PageTitleService,
   private toastr: ToastrService
 ) {}

  ngOnInit(): void {
    console.log('JobPostComponent ngOnInit called');
    this.pageTitle.setTitle('Job Post');
    this.loadJobs();
  }

 loadJobs(): void {
    console.log('loadJobs called - Page:', this.pageNumber, 'Size:', this.pageSize);
    this.isLoading = true;
    this.errorMessage = '';

    this.jobService.getJobs(this.pageNumber, this.pageSize).subscribe({
      next: (response) => {
        if (response.data.items) {
          this.jobs = response.data.items;
          this.totalCount = response.data.totalCount || response.data.items.length;
        } else if (Array.isArray(response)) {
          this.totalCount = response.length;
          const startIndex = (this.pageNumber - 1) * this.pageSize;
          const endIndex = startIndex + this.pageSize;
          this.jobs = response.slice(startIndex, endIndex);
        } else {
          this.jobs = [];
          this.totalCount = 0;
        }

        this.totalPages = Math.ceil(this.totalCount / this.pageSize) || 1;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error loading jobs', err);
        this.errorMessage = 'Failed to load jobs. Please try again later.';
        this.isLoading = false;
        this.jobs = [];
        this.totalCount = 0;
      }
    });
  }

  toggleCreateForm() {
    this.showCreateForm = !this.showCreateForm;
  }

    postJob() {
    console.log('Posting job:', this.job);
    if (!this.isFormValid()) {
      this.toastr.error('Please fill all fields correctly', 'Validation Error');
      return;
    }

    const jobToSend = { ...this.job };
    jobToSend.jobId = null;

    this.jobService.createJob(jobToSend).subscribe({
      next: (newJob) => {
        this.resetForm();
        this.handleSuccess();
        this.toastr.success('Job posted successfully!', 'Job Created');
        console.log('Job posted successfully:', newJob);
        this.loadJobs();
      },
      error: (err) => {
        console.error('Error posting job', err);
        this.toastr.error('Failed to create job. Please try again.', 'Create Failed');
      }
    });
  }

   updateJob() {
    console.log('Updating job:', this.job);
    if (this.isFormValid()) {
      if (this.job.jobId !== null && this.job.jobId !== undefined) {
        this.jobService.updateJob(this.job.jobId, this.job).subscribe({
          next: (updatedJob) => {
            console.log('Job updated successfully:', updatedJob);
            this.toastr.info('Job updated successfully!', 'Job Updated');
            this.handleSuccess();
            this.loadJobs();
          },
          error: (err) => {
            console.error('Error updating job', err);
            this.toastr.error('Failed to update job. Please try again.', 'Update Failed');
          }
        });
      } else {
        this.toastr.error('Invalid job ID', 'Validation Error');
      }
    } else {
      this.toastr.error('Please fill all fields', 'Validation Error');
    }
  }

 openModal(mode: 'create' | 'edit', jobData: any = null) {
  this.modalMode = mode;
  if (mode === 'edit' && jobData && typeof jobData === 'object' && !Array.isArray(jobData)) {
    this.job = { ...jobData };
  } else {
    this.resetForm();
  }
 }
  resetForm() {
    this.job = {
      jobId:null,
      jobTitle: '',
      company: '',
      description: '',
      jobType: '',
      location: '',
      mode: '',
      salaryPackage: null,
      requirements: ''
    };
  }

  prepareDelete(index: number, jobId: number, jobTitle: string) {
    this.jobToDelete = { index, id: jobId, title: jobTitle };
  }

  openDeleteModal(index: number, jobId: number, jobTitle: string) {
    this.prepareDelete(index, jobId, jobTitle);
    const modalElement = document.getElementById('deleteConfirmModal');
    if (modalElement && typeof bootstrap !== 'undefined') {
      try {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
      } catch (error) {
        console.error('Error opening delete modal:', error);
        // Fallback: show confirmation dialog
        if (confirm(`Are you sure you want to delete the job "${jobTitle}"?`)) {
          this.confirmDelete();
        }
      }
    } else {
      // Fallback: show confirmation dialog
      if (confirm(`Are you sure you want to delete the job "${jobTitle}"?`)) {
        this.confirmDelete();
      }
    }
  }

  confirmDelete() {
    if (!this.jobToDelete) return;

    this.isDeleting = true;
    this.jobService.deleteJob(this.jobToDelete.id).subscribe({
      next: () => {
        this.toastr.warning('Job deleted successfully!', 'Job Deleted');
        this.closeDeleteModal();
        this.loadJobs();
      },
      error: (err) => {
        console.error('Error deleting job', err);
        this.toastr.error('Failed to delete job. Please try again.', 'Delete Failed');
        this.isDeleting = false;
      }
    });
  }

  closeDeleteModal() {
    const modalElement = document.getElementById('deleteConfirmModal');
    if (modalElement && typeof bootstrap !== 'undefined') {
      try {
        const modalInstance = bootstrap.Modal.getInstance(modalElement);
        if (modalInstance) {
          modalInstance.hide();
        }
      } catch (error) {
        console.error('Error closing modal:', error);
        // Fallback: manually hide the modal
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        document.body.classList.remove('modal-open');
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
          backdrop.remove();
        }
      }
    }
    this.jobToDelete = null;
    this.isDeleting = false;
  }

   private isFormValid(): boolean {
    const {
      jobTitle, company, description, jobType, location, mode, salaryPackage
    } = this.job;

    return !!(jobTitle && company && description && jobType && location && mode && salaryPackage != null);
  }

  private handleSuccess() {
    this.showCreateForm = false;
    const modalElement = document.getElementById('jobModal');
    if (modalElement && typeof bootstrap !== 'undefined') {
      try {
        let bsModal = bootstrap.Modal.getInstance(modalElement);
        if (!bsModal) {
          bsModal = new bootstrap.Modal(modalElement);
        }
        bsModal.hide();
        this.closeModal();
      } catch (error) {
        console.error('Error handling modal success:', error);
        this.closeModal();
      }
    }
  }


  closeModal() {
    const modalElement = document.getElementById('jobModal');
    if (modalElement && typeof bootstrap !== 'undefined') {
      try {
        const modalInstance = bootstrap.Modal.getInstance(modalElement);
        if (modalInstance) {
          modalInstance.hide();
        }
      } catch (error) {
        console.error('Error closing modal:', error);
      }
    }

    setTimeout(() => {
      document.body.classList.remove('modal-open');
      document.body.style.removeProperty('overflow');
      document.body.style.removeProperty('padding-right');
      document.querySelectorAll('.modal-backdrop').forEach(b => b.remove());
    }, 500);
  }

  // Pagination methods
  nextPage(): void {
    if (this.pageNumber < this.totalPages) {
      this.pageNumber++;
      this.loadJobs();
    }
  }

  prevPage(): void {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.loadJobs();
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.pageNumber) {
      this.pageNumber = page;
      this.loadJobs();
    }
  }

  onPageSizeChange(): void {
    this.pageNumber = 1;
    this.loadJobs();
  }

  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.pageNumber - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getStartIndex(): number {
    return (this.pageNumber - 1) * this.pageSize + 1;
  }

  getEndIndex(): number {
    return Math.min(this.pageNumber * this.pageSize, this.totalCount);
  }
}
