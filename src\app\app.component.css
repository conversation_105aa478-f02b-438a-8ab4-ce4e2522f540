.custom-appRoot-container {
    display: block;
    /* height: 100%; */
    width: 100%;
    margin: 0;
    padding: 0;
    position: relative;

    background-color: #e9ecef;
}


.layout-nav {
    display: block;
    height: 80px;
    width: 100%;
    margin: 0;
    padding: 0;
    position: relative;
    top: 0;

    /* text-decoration: underline; */
    background-color: var(--purple-color);
}

.layout-body {
    display: block;
    margin: 0;
    padding: 0;
    width: 100%;
    height: fit-content;
    position: relative;

    background-color: black;
}

.layout-footer {
    display: flex;
    margin: 0;
    padding: 0;
    width: 100%;
    align-items: center;
    justify-content: center;


    position: relative;
    bottom: 0;

    /* background-color: rgb(72, 115, 104); */
    background-image: linear-gradient(45deg, #a67c1d, #a67c1d)
}