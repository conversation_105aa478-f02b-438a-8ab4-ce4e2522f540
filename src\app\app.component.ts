import { Component, inject, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { AccountService } from '../Utils/_services/account.service';
import { BodyComponent } from "../Components/Layout/2.Body/body.component";
import { FooterComponent } from "../Components/Layout/3.Footer/footer.component";
import { NavComponent } from "../Components/Layout/1.Nav/nav.component";
import { filter } from 'rxjs';


@Component({
  selector: 'app-root',
    standalone: true,
    imports: [
    RouterOutlet
],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent implements OnInit {
  
  showNavbar = true;

  private readonly _accountService: AccountService;
  private router = inject(Router);

  constructor(accountService: AccountService) {
    this._accountService = accountService;

     // Detect route changes
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        // Hide navbar/footer for all routes starting with "/admin"
        this.showNavbar = !event.urlAfterRedirects.startsWith('/admin');
      });
  }
  

  ngOnInit(): void {
    // User session is now automatically initialized in AccountService constructor
  }
}
