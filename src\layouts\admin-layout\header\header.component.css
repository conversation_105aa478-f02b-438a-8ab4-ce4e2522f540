/* .admin-user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(0, 123, 255, 0.2);
} */

.admin-user-name {
  font-weight: 600;
  color: #333;
  text-transform: capitalize;
}

.admin-user-role {
  font-size: 0.8rem;
  color: #007bff;
  font-style: italic;
  text-transform: uppercase;
}

.navbar-brand {
  font-weight: 600;
  color: #333;
}
a.menucollapse-btn {
    color: #000;
    position: absolute;
    left: -36px;
    border: 1px solid #000;
    padding: 5px 6px;
    border-radius: 6px;
    line-height: 0;
    top: 18px;
}
.dashboard-search {
  position: relative;
}

.dashboard-search input {
  max-width: 250px;
  padding: 8px 12px 8px 36px;
  border: 1px solid #ccc;
  border-radius: 25px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.dashboard-search input:focus {
  outline: none;
  border-color: var(--darkgold-color);
}

.dashboard-search .search-icon {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  color: #aaa;
  pointer-events: none;
  font-size: 14px;
}

.logout-btn {
  PADDING: 6px 15px;
}
