<nav class="navbar navbar-expand-lg navbar-light bg-light">
  <div class="px-4 d-flex align-items-center justify-content-between w-100">
    <a class="menucollapse-btn" (click)="toggleSidebar.emit()">
      <i class="fa-solid fa-bars"></i>
    </a>
    <!-- <h5 class="mb-0">{{pageTitle}}</h5> -->
     <div class="dashboard-search">
      <input type="text" placeholder="Search..." />
      <i class="fas fa-search search-icon"></i>
    </div>


    <!-- Admin User Info -->
    <div class="navbar-nav ms-auto" *ngIf="currentUser$ | async as user">
      <div class="nav-item dropdown">
        <div class="admin-user-info">
          <!-- <span class="admin-user-name">{{ user.firstName }}</span>
          <span class="admin-user-role" *ngIf="user.role">({{ user.role }})</span> -->
          <button class="btn btn-outline-danger btn-sm logout-btn" (click)="logout()">
            <i class="fa-solid fa-arrow-right-from-bracket"></i> Logout
          </button>
        </div>
      </div>
    </div>
    
  </div>
</nav>
