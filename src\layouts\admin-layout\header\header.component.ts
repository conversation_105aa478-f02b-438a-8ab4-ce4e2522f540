import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { PageTitleService } from '../../../Utils/_services/page-title.service';
import { AccountService } from '../../../Utils/_services/account.service';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { IUserLogin } from '../../../Components/Home/Account/Register/register.component.model';
import { CommonModule, AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, AsyncPipe],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent implements OnInit {
  @Output() toggleSidebar = new EventEmitter<void>();

  currentUser$: Observable<IUserLogin | null>;
  pageTitle = 'Admin Panel';

  constructor(
    private pageTitleService: PageTitleService,
    private accountService: AccountService,
    private router: Router,
    private toastr: ToastrService
  ) {
    this.currentUser$ = this.accountService.currentUser$;
  }

  onToggleClick() {
    this.toggleSidebar.emit();
  }

  ngOnInit(): void {
    this.pageTitleService.title$.subscribe(title => {
      this.pageTitle = title;
    });

    // User session is now automatically initialized in AccountService constructor
  }

  logout() {
    this.accountService.logout();
    this.toastr.success('Logged out successfully!');
    this.router.navigate(['/login']);
  }
}
