.sidebar {
  min-height: 100%;
  background-color: #f8f9fa;
  border-right: 1px solid #dee2e6;
  transition: all 0.3s ease;
  width: 250px;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-heading {
  color: #333333;
  border-bottom: 1px solid #dee2e6;
  height: 64px;
}

.sidebar.collapsed .sidebar-heading h4 {
  display: none;
}
.list-group-flush {
  margin: 10px;
}
.sidebar-item {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 10px 20px 10px 15px;
  text-decoration: none;
  font-size: 15px;
  margin-bottom: 8px;
}

.sidebar-item:hover {
  background-color: #e9ecef !important;
  color: #495057 !important;
  border-radius: 12px;
  text-decoration: none;
}

/* Active/Selected state styles */
.sidebar-item.active,
.sidebar-item.selected {
  background-color: #bc8710 !important;
  color: white !important;
  border-left: 4px solid #0056b3;
  font-weight: 500;
  border-radius: 12px;
}

.sidebar-item.active:hover,
.sidebar-item.selected:hover {
  background-color: #bc8710 !important;
  color: white !important;
}

.sidebar-item.active i,
.sidebar-item.selected i {
  color: white;
}

/* Icon styles */
.sidebar-item i {
  color: #bc8710;
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
  transition: color 0.2s ease;
}

/* Collapsed sidebar styles */
.sidebar.collapsed .sidebar-item span {
  display: none;
}

.sidebar.collapsed .sidebar-item {
  justify-content: center;
  padding: 12px 10px;
}

.sidebar.collapsed .sidebar-item i {
  font-size: 1.3rem;
}
.text-center {
    text-align: center !important;
    color: #000;
    font-size: 50px !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    min-height: auto;
  }
  .sidebar.collapsed {
    width: 65px;
  }
}

/* Additional visual enhancements */
.sidebar-item {
  position: relative;
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s;
}

.sidebar-item:hover::before {
  left: 100%;
}

/* Focus states for accessibility */
.sidebar-item:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

.sidebar-item.active:focus,
.sidebar-item.selected:focus {
  outline-color: white;
}