import { CommonModule } from '@angular/common';
import { Component, HostListener, Input, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { filter } from 'rxjs';

interface SidebarItem {
  label: string;
  icon?: string;
  route: string;
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css'
})
export class SidebarComponent implements OnInit {
  @Input() collapsed: boolean = false;
  currentRoute = '';

  sidebarItems: SidebarItem[] = [
    { label: 'Dashboard', icon: 'bi-speedometer2', route: '/admin/dashboard' },
    { label: 'App Management', icon: 'bi-grid', route: '/admin/app-management' },
    { label: 'Post Jobs', icon: 'bi-briefcase', route: '/admin/job-post' },
    { label: 'Applied Jobs', icon: 'fa-regular bi-clipboard', route: '/admin/appliedjobs' },
    { label: 'Manage Users', icon: 'bi-people', route: '/admin/users' },
    { label: 'Newsletter Blog', icon: 'bi-journal', route: '/admin/newsletters' },
    { label: 'Bug Reporting', icon: 'bi-bug', route: '/admin/bug-reporting' },
  ];

  constructor(private router: Router) {
    // Listen to route changes
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.currentRoute = event.url;
    });

    // Initial route setup
    this.currentRoute = this.router.url;
  }

  ngOnInit(): void {
    this.setCollapsedBasedOnWidth();
  }

  @HostListener('window:resize', [])
  onResize(): void {
    this.setCollapsedBasedOnWidth();
  }

  private setCollapsedBasedOnWidth(): void {
    this.collapsed = window.innerWidth <= 991;
  }

  navigate(item: SidebarItem): void {
    this.router.navigate([item.route]);
  }

  isActive(route: string): boolean {
    return this.currentRoute === route;
  }
}
