import { Component } from '@angular/core';
import { NavComponent } from "../../Components/Layout/1.Nav/nav.component";
import { FooterComponent } from "../../Components/Layout/3.Footer/footer.component";
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-user-layout',
  standalone: true,
  imports: [NavComponent, FooterComponent, RouterModule],
  templateUrl: './user-layout.component.html',
  styleUrl: './user-layout.component.css'
})
export class UserLayoutComponent {

}
