import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AppliedJobService {

  private baseUrl = 'http://localhost:5001/api/appliedJob';
    
  constructor(private http: HttpClient) {}
  
  createAppliedJob(formData: FormData) {
    return this.http.post(`${this.baseUrl}`, formData);
  }

  getAllAppliedJobs(pageNumber?: number, pageSize?: number): Observable<any> {
  let params: any = {};
  if (pageNumber !== undefined && pageSize !== undefined) {
    params.pageNumber = pageNumber;
    params.pageSize = pageSize;
  }
  return this.http.get(this.baseUrl, { params });
  }

  getAppliedJobById(id: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/${id}`);
  }

  deleteAppliedJob(id: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${id}`);
  }

}
