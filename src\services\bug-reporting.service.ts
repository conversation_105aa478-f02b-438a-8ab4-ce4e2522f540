import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BugReportingEntityDto, PagedResult } from '../models/bug-report.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class BugReportingService {

  private baseUrl = 'http://localhost:5001/api/bug-reporting';
    
  constructor(private http: HttpClient) {}

   getAll(pageNumber: number, pageSize: number): Observable<any> {
    const params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString());
    return this.http.get<any>(this.baseUrl, { params });
  }

  getById(id: string): Observable<BugReportingEntityDto> {
    return this.http.get<BugReportingEntityDto>(`${this.baseUrl}/${id}`);
  }

  create(report: BugReportingEntityDto): Observable<BugReportingEntityDto> {
    return this.http.post<BugReportingEntityDto>(this.baseUrl, report);
  }

  update(id: string, report: BugReportingEntityDto): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}/${id}`, report);
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }

}
